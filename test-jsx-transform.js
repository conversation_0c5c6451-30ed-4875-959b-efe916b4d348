const CustomVueTransformer = require('./src/frameworks/vue/CustomVueTransformer');
const RenderContentTransformer = require('./src/frameworks/vue/RenderContentTransformer');

const transformer = new CustomVueTransformer();
const renderTransformer = new RenderContentTransformer();

// 测试简单的JSX转换
const testCode = `
<script>
export default {
  methods: {
    renderContent(h, { node, data }) {
      return (
        <div class="connect_id_title">
          {data.title}
          <span class="connect_id_number" style="opacity:0">
            {data.id}
          </span>
        </div>
      )
    },
  }
}
</script>`;

console.log('原始代码:');
console.log(testCode);
console.log('\n转换后的代码 (CustomVueTransformer):');
const result = transformer.transform(testCode);
console.log(result);

console.log('\n转换后的代码 (RenderContentTransformer):');
const renderResult = renderTransformer.transform(testCode);
console.log(renderResult);
