<template>
  <div>
    <el-tree
      :render-content="renderContent"
      :data="treeData"
    />
  </div>
</template>

<script>
import { h } from 'vue'

export default {
  data() {
    return {
      treeData: []
    }
  },
  methods: {
    renderContent({ node, data  }) {
      // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        h('div', { class: 'connect_id_title' }, [h('span', null, data.title), h('span', { class: 'connect_id_number', style: { opacity: 0 } }, data.id)])
      )
    },
    expandAll(expand) {
      // 展开/折叠逻辑
    }
  }
}
</script>

<style scoped>
.connect_id_title {
  display: flex;
  align-items: center;
}
.connect_id_number {
  margin-left: 10px;
}
</style>