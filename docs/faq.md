// make search results more in line with expectations
import Fuse from 'fuse.js'
import path from 'path'

const version = require('element-ui/package.json').version // element-ui version from node_modules

->

const version = require('element-plus/package.json').version // element-plus version from node_modules

# Vue 3 + Element Plus 项目常见问题 FAQ

## 🔧 SVG Sprite Loader 相关问题

### Q1: 遇到 "Cannot find module 'webpack/lib/RuleSet'" 错误

**问题描述：**
```
ERROR in ./src/icons/svg/chart.svg
Module build failed (from ./node_modules/.pnpm/svg-sprite-loader@4.1.3_webpack@5.99.9/node_modules/svg-sprite-loader/lib/loader.js):
Error: Cannot find module 'webpack/lib/RuleSet'
```

**原因：**
`svg-sprite-loader` 4.1.3 版本与 webpack 5 不兼容

**解决方案：**
```bash
# 卸载旧版本并安装兼容版本
pnpm remove svg-sprite-loader
pnpm add -D svg-sprite-loader@6.0.11
```

---

## 🎨 SCSS 样式相关问题

### Q2: SCSS 变量未定义错误

**问题描述：**
```
Syntax Error: Undefined variable.
  ╷
6 │     margin-left: $sideBarWidth;
  │                  ^^^^^^^^^^^^^
  ╵
  src/styles/sidebar.scss 6:18
```

**原因：**
使用 `@use` 语法时，变量不会自动跨文件共享

**解决方案：**
在需要使用变量的 SCSS 文件顶部添加：
```scss
@use 'variables' as *;
```

---

## ⚛️ Vue 3 组件相关问题

### Q3: 函数式组件语法错误

**问题描述：**
```
TypeError: Cannot read properties of null (reading 'content')
```

**原因：**
Vue 2 的函数式组件语法在 Vue 3 中已改变

**解决方案：**
```javascript
// Vue 2 写法 (错误)
export default {
  functional: true,
  render(h, context) {
    const { icon, title } = context.props
    // ...
  }
}

// Vue 3 写法 (正确)
import { h } from 'vue'
export default {
  props: { /* ... */ },
  render() {
    const { icon, title } = this
    return h('div', /* ... */)
  }
}
```

---

## 🛠️ Webpack 5 兼容性问题

### Q4: Node.js 核心模块 polyfill 缺失

**问题描述：**
```
Module not found: Error: Can't resolve 'path' in '...'
Module not found: Error: Can't resolve 'stream' in '...'
```

**原因：**
Webpack 5 不再自动提供 Node.js 核心模块的 polyfill

**解决方案：**
1. 安装 polyfill 包：
```bash
pnpm add -D path-browserify stream-browserify
```

2. 在 `vue.config.js` 中配置 fallback：
```javascript
module.exports = {
  configureWebpack: {
    resolve: {
      fallback: {
        "path": require.resolve("path-browserify"),
        "stream": require.resolve("stream-browserify")
      }
    }
  }
}
```

---

## 📦 依赖兼容性问题

### Q5: html-webpack-plugin 版本不兼容

**问题描述：**
```
ERROR TypeError: htmlWebpackPlugin.getHooks is not a function
```

**解决方案：**
```bash
# 升级到兼容版本
pnpm add -D html-webpack-plugin@5.6.0
```

同时移除不兼容的插件配置：
```javascript
// 在 vue.config.js 中移除 script-ext-html-webpack-plugin 配置
// 删除以下代码块：
config
  .plugin('ScriptExtHtmlWebpackPlugin')
  .after('html')
  .use('script-ext-html-webpack-plugin', [/* ... */])
```

---

## 🔄 从 Element UI 迁移到 Element Plus

### Q6: Element UI 模块找不到

**问题描述：**
```
Module not found: Error: Can't resolve 'element-ui' in '...'
```

**解决方案：**
1. 卸载 Element UI，安装 Element Plus：
```bash
pnpm remove element-ui
pnpm add element-plus @element-plus/icons-vue
```

2. 更新导入语句：
```javascript
// 旧的 Element UI 导入
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// 新的 Element Plus 导入
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
```

---

## 🔍 ESLint 配置问题

### Q7: ESLint 规则配置错误

**问题描述：**
```
Configuration for rule "vue/max-attributes-per-line" is invalid
```

**解决方案：**
更新 `.eslintrc.js` 中的 Vue 规则配置：
```javascript
rules: {
  'vue/max-attributes-per-line': [2, {
    'singleline': 10,
    'multiline': {
      'max': 1,
      'allowFirstLine': false
    }
  }]
}
```

---

## 📋 完整修复流程总结

1. **升级 svg-sprite-loader** 到 webpack 5 兼容版本
2. **修复 SCSS 变量导入** 使用 `@use` 语法
3. **更新 Vue 3 组件语法** 特别是函数式组件
4. **配置 webpack polyfills** 解决 Node.js 模块问题
5. **升级相关插件** 确保 webpack 5 兼容性
6. **迁移到 Element Plus** 替换 Element UI

按照以上步骤，可以成功解决 Vue 3 + Element Plus 项目中的常见构建问题。



好的，我来整理一个完整的Vue 2 到 Vue 3 + Element UI 到 Element Plus 迁移FAQ，包含所有遇到的错误和对应的解决方案：

# Vue 2 → Vue 3 + Element UI → Element Plus 迁移 FAQ

## 1. Element UI 依赖错误

### ❌ 错误信息
```
Module not found: Error: Can't resolve 'element-ui' in '/Users/<USER>/src'
Module not found: Error: Can't resolve 'element-ui/lib/locale/lang/en'
```

### ✅ 解决方案
**修改 `src/main.js`：**
```javascript
// 旧版 (Vue 2 + Element UI)
import Vue from 'vue'
import Element from 'element-ui'
import './styles/element-variables.scss'
import enLang from 'element-ui/lib/locale/lang/en'

Vue.use(Element, {
  size: Cookies.get('size') || 'medium',
  locale: enLang
})

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})

// 新版 (Vue 3 + Element Plus)
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import enLang from 'element-plus/es/locale/lang/en'

const app = createApp(App)

app.use(ElementPlus, {
  size: Cookies.get('size') || 'default',
  locale: enLang
})

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(store).use(router)
app.mount('#app')
```

## 2. Element UI 组件引用错误

### ❌ 错误信息
```javascript
import { MessageBox, Message } from 'element-ui'
```

### ✅ 解决方案
**修改所有文件中的 Element UI 组件引用：**
```javascript
// 旧版
import { MessageBox, Message } from 'element-ui'

// 新版
import { ElMessageBox, ElMessage } from 'element-plus'

// 使用时也要更新
Message.error('错误') → ElMessage.error('错误')
MessageBox.confirm() → ElMessageBox.confirm()
```

## 3. Vue Router 4 API 变更错误

### ❌ 错误信息
```javascript
router.addRoutes is not a function
export 'default' (imported as 'Router') was not found in 'vue-router'
```

### ✅ 解决方案
**修改 `src/permission.js`：**
```javascript
// 旧版 (Vue Router 3)
router.addRoutes(accessRoutes)

// 新版 (Vue Router 4)
accessRoutes.forEach(route => {
  router.addRoute(route)
})
```

**修改路由创建方式：**
```javascript
// 旧版
import Router from 'vue-router'
Vue.use(Router)
export default new Router({...})

// 新版
import { createRouter, createWebHashHistory } from 'vue-router'
export default createRouter({
  history: createWebHashHistory(),
  routes: [...]
})
```

## 4. 缺失依赖包错误

### ❌ 错误信息
```
Module not found: Error: Can't resolve 'vue-splitpane'
```

### ✅ 解决方案
**替换为 Vue 3 兼容的包：**
```javascript
// 旧版
import splitPane from 'vue-splitpane'

// 新版
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

// 模板也要更新
<split-pane split="vertical">
  <template slot="paneL">...</template>
  <template slot="paneR">...</template>
</split-pane>

// 改为
<splitpanes class="default-theme">
  <pane>...</pane>
  <pane>...</pane>
</splitpanes>
```

## 5. ESLint 配置错误

### ❌ 错误信息
```
Configuration for rule "vue/max-attributes-per-line" is invalid:
Value {"max":1,"allowFirstLine":false} should be number.
```

### ✅ 解决方案
**修改 `.eslintrc.js`：**
```javascript
// 旧版
"vue/max-attributes-per-line": [2, {
  "singleline": 10,
  "multiline": {
    "max": 1,
    "allowFirstLine": false
  }
}]

// 新版
"vue/max-attributes-per-line": [2, {
  "singleline": 10,
  "multiline": 1
}]
```

## 6. babel-eslint 解析错误

### ❌ 错误信息
```
Parsing error: require() of ES Module ... from babel-eslint not supported.
```

### ✅ 解决方案
**更新 ESLint 配置：**
```javascript
// 旧版
parserOptions: {
  parser: 'babel-eslint',
  sourceType: 'module'
}

// 新版 - 使用 @babel/eslint-parser
parserOptions: {
  parser: '@babel/eslint-parser',
  sourceType: 'module',
  requireConfigFile: false
}
```

## 7. 缺失视图文件错误

### ❌ 错误信息
```
Module not found: Error: Can't resolve '@/views/nested/menu1/index'
```

### ✅ 解决方案
**创建缺失的视图文件：**
```javascript
// src/views/nested/menu1/index.vue
<template>
  <div>
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'Menu1'
}
</script>
```

## 8. Vue 3 过滤器移除错误

### ❌ 错误信息
```javascript
Vue.filter is not a function
```

### ✅ 解决方案
**将过滤器改为全局属性：**
```javascript
// 旧版
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

// 新版
app.config.globalProperties.$filters = filters

// 模板中使用
{{ value | dateFormat }} → {{ $filters.dateFormat(value) }}
```

## 9. Sass 语法警告

### ⚠️ 警告信息
```
::v-deep usage as a combinator has been deprecated. Use :deep(<inner-selector>) instead
Using / for division outside of calc() is deprecated
```

### ✅ 解决方案
```scss
/* 旧版 */
::v-deep .el-input {
  color: red;
}
$width: 100px / 2;

/* 新版 */
:deep(.el-input) {
  color: red;
}
$width: math.div(100px, 2); // 或 calc(100px / 2)
```

## 10. Mock 文件缺失错误

### ❌ 错误信息
```
Module not found: Error: Can't resolve '../mock'
```

### ✅ 解决方案
**创建基础 mock 文件：**
```javascript
// mock/index.js
export function mockXHR() {
  console.log('Mock XHR initialized')
}
```

## 11. Vuex 4 API 变更

### ❌ 错误信息
```javascript
store.dispatch returns undefined
```

### ✅ 解决方案
**更新 Vuex 创建方式：**
```javascript
// 旧版
import Vuex from 'vuex'
Vue.use(Vuex)
export default new Vuex.Store({...})

// 新版
import { createStore } from 'vuex'
export default createStore({...})
```

## 12. 组合式 API 迁移

### ✅ 可选方案
**逐步迁移到组合式 API：**
```javascript
// 选项式 API (兼容)
export default {
  data() {
    return { count: 0 }
  },
  methods: {
    increment() {
      this.count++
    }
  }
}

// 组合式 API (推荐)
import { ref } from 'vue'
export default {
  setup() {
    const count = ref(0)
    const increment = () => count.value++
    return { count, increment }
  }
}
```

## 📋 迁移检查清单

- [ ] 更新 package.json 依赖
- [ ] 修改 main.js 入口文件
- [ ] 替换所有 Element UI 组件引用
- [ ] 更新 Vue Router 配置和 API
- [ ] 修复 ESLint 配置
- [ ] 创建缺失的视图文件
- [ ] 更新 Vuex 配置
- [ ] 处理过滤器迁移
- [ ] 修复 Sass 语法警告
- [ ] 测试构建和开发环境
- [ ] 逐步迁移到组合式 API (可选)

## 🎯 最终验证

```bash
# 开发环境测试
npm run dev

# 生产构建测试
npm run build:prod

# ESLint 检查
npm run lint
```

这个FAQ涵盖了从Vue 2 + Element UI 迁移到 Vue 3 + Element Plus 过程中遇到的所有主要问题和解决方案。


好的，没问题。这是为您整理的 Vue 2 迁移到 Vue 3 的常见问题（FAQ）中文版，希望能帮助到您和其他开发者。

### Vue 2 到 Vue 3 迁移常见问题（FAQ）

这里总结了一些在将类似 `vue-element-admin` 的 Vue 2 项目迁移到 Vue 3 时遇到的常见问题及其解决方案。

---

#### **问题 1：升级 Vuex 后，应用启动时报错 "Cannot read properties of undefined (reading 'use')"，如何修复？**

**答：** 这个错误通常是因为 Vuex 4 (用于 Vue 3) 的初始化 API 与 Vuex 3 不同了。在 Vue 3 中，你不再需要通过 `Vue.use(Vuex)` 来“安装”Vuex。

*   **问题所在 (Vue 2 / Vuex 3 写法):**
    你的 `src/store/index.js` 文件可能还是这样写的：
    ```javascript
    import Vue from 'vue'
    import Vuex from 'vuex'

    Vue.use(Vuex)

    const store = new Vuex.Store({
      // ...
    })

    export default store
    ```

*   **解决方案 (Vue 3 / Vuex 4 写法):**
    你需要使用新的 `createStore` 函数来创建 store。
    1.  修改你的 import 语句。
    2.  用 `createStore()` 替换 `new Vuex.Store()`。
    3.  移除 `Vue.use(Vuex)` 这一行。

    更新后的 `src/store/index.js` 应该如下所示：
    ```javascript
    import { createStore } from 'vuex'
    import getters from './getters'
    // ... 其他模块导入

    const store = createStore({
      modules,
      getters
    })

    export default store
    ```

---

#### **问题 2：如何为 Vue 3 更新我的 Vue Router 路由配置？**

**答：** Vue Router 4 引入了一些重大变更，包括路由实例的创建方式和历史模式（history）的管理方式。

*   **问题所在 (Vue 2 / Vue Router 3 写法):**
    你的 `src/router/index.js` 可能还在使用 `new Router()` 和基于字符串的 `mode` 配置。同时，捕获所有路由（"catch-all"）的语法也不同了。
    ```javascript
    import Vue from 'vue'
    import Router from 'vue-router'

    Vue.use(Router)

    export default new Router({
      mode: 'history', // 旧的写法
      routes: [
        // ...
        { path: '*', redirect: '/404', hidden: true } // 旧的通配符路由
      ]
    })
    ```

*   **解决方案 (Vue 3 / Vue Router 4 写法):**
    使用新的 `createRouter` 和 `createWebHistory` (或 `createWebHashHistory`) 函数。
    1.  更新 `vue-router` 的 import 语句。
    2.  使用 `createRouter` 来创建路由实例。
    3.  提供一个 history 实现 (例如，`createWebHistory()` 对应 'history' 模式)。
    4.  更新通配符路由的语法。

    更新后的 `src/router/index.js` 大致如下：
    ```javascript
    import { createRouter, createWebHistory } from 'vue-router'
    // ...

    const router = createRouter({
      history: createWebHistory(process.env.BASE_URL),
      routes: constantRoutes
    })

    // 在你的路由数组中，需要更新通配符路由：
    // { path: '*', redirect: '/404' } 需要被修改为：
    { path: '/:pathMatch(.*)*', redirect: '/404', hidden: true }

    export default router
    ```

---

#### **问题 3：在 Vue 3 中如何注册全局组件（例如，一个全局的 SVG 图标组件）？**

**答：** 全局组件现在需要注册在由 `createApp` 创建的 `app` 实例上，而不是在全局的 `Vue` 对象上。

*   **问题所在 (Vue 2 写法):**
    你可能有一个文件，在其中直接使用 `Vue.component` 注册全局组件。
    ```javascript
    // src/icons/index.js
    import Vue from 'vue'
    import SvgIcon from '@/components/SvgIcon'
    Vue.component('svg-icon', SvgIcon)
    ```

*   **解决方案 (Vue 3 写法):**
    1.  修改组件文件 (`src/icons/index.js`)，让它导出组件而不是直接注册。
    2.  在 `src/main.js` 中导入该组件，并使用 `app.component()` 在 `app` 实例上注册。

    **第一步: 导出组件 (`src/icons/index.js`)**
    ```javascript
    import SvgIcon from '@/components/SvgIcon'
    // ... require.context 的代码 ...
    export { SvgIcon } // 导出组件
    ```
    **第二步: 在 `main.js` 中注册**
    ```javascript
    import { createApp } from 'vue'
    import App from './App'
    import { SvgIcon } from './icons' // 导入你的组件

    const app = createApp(App)

    app.component('svg-icon', SvgIcon) // 在 app 实例上注册全局组件

    app.mount('#app')
    ```

---

#### **问题 4：如何在 Vue 3 中配置全局错误处理器？**

**答：** 全局错误处理器从 `Vue.config.errorHandler` 移到了 `app.config.errorHandler`。

*   **问题所在 (Vue 2 写法):**
    ```javascript
    // src/utils/error-log.js
    import Vue from 'vue'
    Vue.config.errorHandler = function(err, vm, info) {
      // ...
    }
    ```

*   **解决方案 (Vue 3 写法):**
    创建一个安装函数（setup function），该函数接收 `app` 实例作为参数，并在该函数中进行配置。
    1.  在你的工具文件中创建一个导出的安装函数。
    2.  在 `main.js` 中调用这个函数，并传入 `app` 实例。

    **第一步: 创建安装函数 (`src/utils/error-log.js`)**
    ```javascript
    export function setupErrorLog(app) {
      app.config.errorHandler = function(err, instance, info) {
        // 注意: `vm` 参数现在是 `instance`
        // ... 你的错误处理逻辑
      }
    }
    ```
    **第二步: 在 `main.js` 中使用它**
    ```javascript
    import { createApp } from 'vue'
    import App from './App'
    import { setupErrorLog } from './utils/error-log'

    const app = createApp(App)
    setupErrorLog(app) // 调用函数以设置错误处理器

    app.mount('#app')
    ```

希望这份 FAQ 对您有帮助！如果您还有其他问题，随时可以提出。


## Before error

```log
> vue-element-admin@4.4.0 dev
> vue-cli-service serve

 INFO  Starting development server...
 ERROR  ValidationError: Invalid options object. Dev Server has been initialized using an options object that does not match the API schema.
         - options has an unknown property 'before'. These properties are valid:
           object { allowedHosts?, bonjour?, client?, compress?, devMiddleware?, headers?, historyApiFallback?, host?, hot?, http2?, https?, ipc?, liveReload?, magicHtml?, onAfterSetupMiddleware?, onBeforeSetupMiddleware?, onListening?, open?, port?, proxy?, server?, setupExitSignals?, setupMiddlewares?, static?, watchFiles?, webSocketServer? }
ValidationError: Invalid options object. Dev Server has been initialized using an options object that does not match the API schema.
 - options has an unknown property 'before'. These properties are valid:
   object { allowedHosts?, bonjour?, client?, compress?, devMiddleware?, headers?, historyApiFallback?, host?, hot?, http2?, https?, ipc?, liveReload?, magicHtml?, onAfterSetupMiddleware?, onBeforeSetupMiddleware?, onListening?, open?, port?, proxy?, server?, setupExitSignals?, setupMiddlewares?, static?, watchFiles?, webSocketServer? }
```

去掉 before，改为 `setupMiddlewares`

````javascript
  devServer: {
    port: process.env.port || process.env.npm_config_port || 9527,
    open: true,
    // overlay: {
    //   warnings: false,
    //   errors: true
    // },
    // before: require('./mock/mock-server.js')
    setupMiddlewares(middlewares, devServer) {
      const mockServer = require('./mock/mock-server.js')
      mockServer(devServer.app)
      return middlewares
    }
  }
````


## ICON 问题

```javascript
import SvgIcon from '@/components/SvgIcon'// svg component

const req = require.context('./svg', false, /\.svg$/)
const requireAll = requireContext => requireContext.keys().map(requireContext)
requireAll(req)

// Export for main.js to register globally
export { SvgIcon }
```


## VueCompilerError: Error parsing JavaScript expression: Unexpected token, expected "," (3:22)

Gogocode 转换出错

修复前:

```vue
<el-button
  @click="
    UpdateVisible = true
    SelectId = scope.row.ruleId
  "
>修改</el-button>
```

修复后:

```vue
<el-button
  @click="() => { UpdateVisible = true; SelectId = scope.row.ruleId; }"
>修改</el-button>
```
