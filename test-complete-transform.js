const SimpleRenderContentTransformer = require('./src/frameworks/vue/SimpleRenderContentTransformer');

const transformer = new SimpleRenderContentTransformer();

// 测试简单的例子
const simpleTest = `
<script>
export default {
  methods: {
    renderContent(h, { node, data }) {
      return (
        <div class="connect_id_title">
          {data.title}
          <span class="connect_id_number" style="opacity:0">
            {data.id}
          </span>
        </div>
      )
    },
  }
}
</script>`;

console.log('=== 简单测试 ===');
console.log('原始代码:');
console.log(simpleTest);
console.log('\n转换后:');
const simpleResult = transformer.transform(simpleTest);
console.log(simpleResult);

// 测试复杂的例子
const complexTest = `
<script>
export default {
  methods: {
    renderContent(h, { node, data }) {
      if (data.id === '-1') {
        return (
          <div class="custom-tree-node">
            {data.title}
            <span class="connect_id_number" style="opacity:0">
              {data.id}
            </span>
            <span>
              <el-button
                type="text"
                size="mini"
                on-click={() => this.expandAll(false)}
              >
                全部折叠
              </el-button>
              <el-button
                type="text"
                size="mini"
                on-click={() => this.expandAll(true)}
              >
                全部展开
              </el-button>
            </span>
          </div>
        )
      } else {
        return (
          <div class="connect_id_title">
            {data.title}
            <span class="connect_id_number" style="opacity:0">
              {data.id}
            </span>
          </div>
        )
      }
    },
  }
}
</script>`;

console.log('\n\n=== 复杂测试 ===');
console.log('原始代码:');
console.log(complexTest);
console.log('\n转换后:');
const complexResult = transformer.transform(complexTest);
console.log(complexResult);
