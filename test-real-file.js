const fs = require('fs');
const RenderContentTransformer = require('./src/frameworks/vue/RenderContentTransformer');

const transformer = new RenderContentTransformer();

// 读取实际的测试文件
const filePath = '/Users/<USER>/works/galaxy/galaxy-vue3-demi/src/views/business/user_pos_manage/connect_user_handle.vue';

try {
  const originalContent = fs.readFileSync(filePath, 'utf8');
  
  console.log('=== 原始文件中的 renderContent 函数 ===');
  // 提取 renderContent 函数
  const renderContentMatch = originalContent.match(/(renderContent\s*\(\s*h\s*,\s*{\s*[^}]+\s*}\s*\)\s*{[\s\S]*?return\s*\([\s\S]*?\)\s*})/);
  if (renderContentMatch) {
    console.log(renderContentMatch[1]);
  } else {
    console.log('未找到 renderContent 函数');
  }
  
  console.log('\n=== 转换后的结果 ===');
  const transformedContent = transformer.transform(originalContent);
  
  // 提取转换后的 renderContent 函数
  const transformedMatch = transformedContent.match(/(renderContent\s*\(\s*{\s*[^}]+\s*}\s*\)\s*{[\s\S]*?return\s*\([\s\S]*?\)\s*})/);
  if (transformedMatch) {
    console.log(transformedMatch[1]);
  } else {
    console.log('转换失败');
  }
  
  // 检查是否添加了 h 导入
  if (transformedContent.includes("import { h } from 'vue'")) {
    console.log('\n✅ 成功添加了 h 函数导入');
  } else {
    console.log('\n❌ 未添加 h 函数导入');
  }
  
} catch (error) {
  console.error('读取文件失败:', error.message);
}
