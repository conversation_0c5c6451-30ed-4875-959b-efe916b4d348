#!/usr/bin/env node

require('dotenv').config();

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const AutoMigrator = require('../src/app/AutoMigrator');
const program = new Command();

program
  .name('vue-migrator')
  .description('Vue 2 到 Vue 3 统一迁移工具')
  .version('1.0.0');

program
  .command('auto <old-project> <new-project>')
  .description('🔄 从旧 Vue 2 工程迁移到新 Vue 3 工程（完整7步迁移流程）')
  .option('--skip-dependency-check', '跳过依赖兼容性检查')
  .option('--skip-ai', '跳过 AI 修复步骤')
  .option('--eslint', '启用 ESLint 自动修复（默认禁用）')
  .option('--skip-build', '跳过构建和构建错误修复')
  .option('--ai-key <key>', 'AI API Key (支持 DeepSeek/GLM/OpenAI)')
  .option('--build-command <cmd>', '构建命令', 'npm run build')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .option('--quiet', '静默模式，仅显示关键信息')
  .action(async (oldProject, newProject, options) => {
    try {
      const oldProjectPath = path.resolve(oldProject);
      const newProjectPath = path.resolve(newProject);

      // 验证源项目路径
      if (!await fs.pathExists(oldProjectPath)) {
        throw new Error(`源项目路径不存在: ${oldProjectPath}`);
      }

      const migrator = new AutoMigrator(oldProjectPath, {
        sourceToTargetMode: true,
        sourceProjectPath: oldProjectPath,
        targetProjectPath: newProjectPath,
        skipDependencyCheck: options.skipDependencyCheck,
        skipAIRepair: options.skipAi,
        skipESLint: !options.eslint,
        skipBuild: options.skipBuild,
        aiApiKey: options.aiKey,
        buildCommand: options.buildCommand,
        dryRun: options.dryRun,
        verbose: options.verbose,
        quiet: options.quiet
      });

      await migrator.migrate();

      // 生成迁移报告
      if (!options.quiet) {
        console.log(chalk.blue('\n📄 生成迁移报告...'));
      }
      await generateMigrationReport(oldProjectPath, newProjectPath, options.quiet);

      console.log(chalk.bold.green('\n🎉 源到目标迁移完成！'));
      if (!options.quiet) {
        console.log(chalk.yellow('\n💡 后续建议:'));
        console.log('1. 检查迁移后的代码是否正常工作');
        console.log('2. 运行测试确保功能正常');
      }

    } catch (error) {
      console.error(chalk.red('\n❌ 迁移失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

async function generateMigrationReport(oldProjectPath, newProjectPath, quiet = false) {
  try {
    const MigrationDocGenerator = require('../src/frameworks/vue/third-party/MigrationDocGenerator');

    if (!quiet) {
      console.log(chalk.gray('正在生成迁移报告...'));
    }
    const docGenerator = new MigrationDocGenerator(oldProjectPath, null, {
      outputPath: path.join(newProjectPath, 'migration-report.md'),
      includeUsageDetails: false,
      language: 'zh-CN'
    });

    const result = await docGenerator.generateMigrationGuide();

    if (result.success) {
      if (!quiet) {
        console.log(chalk.green(`✅ 迁移报告已生成: ${result.outputPath}`));
      }
    } else {
      if (!quiet) {
        console.log(chalk.yellow('⚠️  迁移报告生成失败'));
      }
    }
  } catch (error) {
    if (!quiet) {
      console.log(chalk.yellow('⚠️  迁移报告生成失败:'), error.message);
    }
  }
}

// 全局错误处理
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  if (process.env.DEBUG) {
    console.error(error.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ 未处理的 Promise 拒绝:'), reason);
  if (process.env.DEBUG) {
    console.error(promise);
  }
  process.exit(1);
});

// 解析命令行参数
program.parse();

// 如果没有提供命令，显示帮助
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
