#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const SimpleRenderContentTransformer = require('./src/frameworks/vue/SimpleRenderContentTransformer');

/**
 * 应用 renderContent JSX 转换到测试项目
 */
class RenderContentFixer {
  constructor(projectPath) {
    this.projectPath = projectPath;
    this.transformer = new SimpleRenderContentTransformer();
    this.stats = {
      total: 0,
      transformed: 0,
      skipped: 0,
      errors: 0
    };
  }

  /**
   * 查找所有包含 renderContent 的 Vue 文件
   */
  findRenderContentFiles() {
    const files = [];
    
    const searchDir = (dir) => {
      const entries = fs.readdirSync(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
          searchDir(fullPath);
        } else if (entry.isFile() && entry.name.endsWith('.vue')) {
          try {
            const content = fs.readFileSync(fullPath, 'utf8');
            if (content.includes('renderContent(h,') || content.includes('renderContent(h, {')) {
              files.push(fullPath);
            }
          } catch (error) {
            console.warn(`警告: 无法读取文件 ${fullPath}: ${error.message}`);
          }
        }
      }
    };

    searchDir(this.projectPath);
    return files;
  }

  /**
   * 转换单个文件
   */
  transformFile(filePath) {
    try {
      console.log(`处理文件: ${path.relative(this.projectPath, filePath)}`);
      
      const originalContent = fs.readFileSync(filePath, 'utf8');
      const transformedContent = this.transformer.transform(originalContent);
      
      if (originalContent !== transformedContent) {
        // 创建备份
        const backupPath = filePath + '.backup';
        fs.writeFileSync(backupPath, originalContent);
        
        // 写入转换后的内容
        fs.writeFileSync(filePath, transformedContent);
        
        console.log(`  ✅ 转换成功 (备份: ${path.basename(backupPath)})`);
        this.stats.transformed++;
        
        // 显示主要变化
        this.showChanges(originalContent, transformedContent);
      } else {
        console.log(`  ⏭️  无需转换`);
        this.stats.skipped++;
      }
      
    } catch (error) {
      console.error(`  ❌ 转换失败: ${error.message}`);
      this.stats.errors++;
    }
  }

  /**
   * 显示主要变化
   */
  showChanges(original, transformed) {
    const changes = [];
    
    if (transformed.includes("import { h } from 'vue'") && !original.includes("import { h } from 'vue'")) {
      changes.push('+ 添加了 h 函数导入');
    }
    
    if (transformed.includes('renderContent({ node, data') && original.includes('renderContent(h, { node, data')) {
      changes.push('+ 转换了函数签名');
    }
    
    if (transformed.includes("h('div'") && original.includes('<div')) {
      changes.push('+ 转换了 JSX 为 h 函数');
    }
    
    if (changes.length > 0) {
      changes.forEach(change => console.log(`    ${change}`));
    }
  }

  /**
   * 执行转换
   */
  run() {
    console.log(`🔍 在项目中查找包含 renderContent 的文件: ${this.projectPath}`);
    
    const files = this.findRenderContentFiles();
    this.stats.total = files.length;
    
    if (files.length === 0) {
      console.log('📝 未找到包含 renderContent 的 Vue 文件');
      return;
    }
    
    console.log(`📁 找到 ${files.length} 个文件需要处理:`);
    files.forEach(file => {
      console.log(`  - ${path.relative(this.projectPath, file)}`);
    });
    
    console.log('\n🔄 开始转换...\n');
    
    files.forEach(file => {
      this.transformFile(file);
    });
    
    this.printSummary();
  }

  /**
   * 打印总结
   */
  printSummary() {
    console.log('\n📊 转换总结:');
    console.log(`  总文件数: ${this.stats.total}`);
    console.log(`  转换成功: ${this.stats.transformed}`);
    console.log(`  跳过文件: ${this.stats.skipped}`);
    console.log(`  转换失败: ${this.stats.errors}`);
    
    if (this.stats.transformed > 0) {
      console.log('\n🎉 renderContent JSX 转换完成！');
      console.log('💡 提示: 原始文件已备份为 .backup 文件');
      console.log('🔧 建议: 运行构建命令验证转换结果');
    }
  }
}

// 主程序
if (require.main === module) {
  const projectPath = process.argv[2] || '/Users/<USER>/works/galaxy/galaxy-vue3-demi';
  
  if (!fs.existsSync(projectPath)) {
    console.error(`❌ 项目路径不存在: ${projectPath}`);
    process.exit(1);
  }
  
  const fixer = new RenderContentFixer(projectPath);
  fixer.run();
}

module.exports = RenderContentFixer;
