const CustomVueTransformer = require('../../../src/frameworks/vue/CustomVueTransformer');

describe('CustomVueTransformer', () => {
  let transformer;

  beforeEach(() => {
    transformer = new CustomVueTransformer();
  });

  describe('transformJsxRenderFunctions', () => {
    it('should transform Vue 2 JSX renderContent to Vue 3 h function', () => {
      const input = `
<script>
export default {
  methods: {
    renderContent(h, { node, data }) {
      // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        <div class="connect_id_title">
          {data.title}{' '}
          <span class="connect_id_number" style="opacity:0">
            {data.id}
          </span>
        </div>
      )
    },
  }
}
</script>`;

      const result = transformer.transform(input);
      
      // 应该包含h函数导入
      expect(result).toContain("import { h } from 'vue'");
      
      // 应该转换函数签名
      expect(result).toContain('renderContent({ node, data })');
      
      // 应该不再包含JSX语法
      expect(result).not.toContain('<div class="connect_id_title">');
      
      // 应该包含h函数调用
      expect(result).toContain("h('div'");
      expect(result).toContain("h('span'");
    });

    it('should handle complex JSX with event handlers', () => {
      const input = `
<script>
export default {
  methods: {
    renderContent(h, { node, data }) {
      return (
        <div class="custom-tree-node">
          {data.title}
          <el-button
            type="text"
            size="mini"
            on-click={() => this.expandAll(false)}
          >
            全部折叠
          </el-button>
        </div>
      )
    },
  }
}
</script>`;

      const result = transformer.transform(input);
      
      // 应该包含h函数导入
      expect(result).toContain("import { h } from 'vue'");
      
      // 应该转换函数签名
      expect(result).toContain('renderContent({ node, data })');
      
      // 应该包含事件处理器转换
      expect(result).toContain('onClick:');
    });

    it('should not add duplicate h import if already exists', () => {
      const input = `
<script>
import { h, ref } from 'vue'

export default {
  methods: {
    renderContent(h, { node, data }) {
      return (
        <div class="test">
          {data.title}
        </div>
      )
    },
  }
}
</script>`;

      const result = transformer.transform(input);
      
      // 应该只有一个h导入
      const hImportMatches = result.match(/import.*h.*from.*vue/g);
      expect(hImportMatches).toHaveLength(1);
    });

    it('should handle self-closing JSX tags', () => {
      const input = `
<script>
export default {
  methods: {
    renderContent(h, { node, data }) {
      return (
        <div>
          <el-button type="primary" />
          <span>{data.title}</span>
        </div>
      )
    },
  }
}
</script>`;

      const result = transformer.transform(input);
      
      expect(result).toContain("h('el-button'");
      expect(result).toContain("type: 'primary'");
    });
  });

  describe('parseJsxAttributes', () => {
    it('should parse class attributes correctly', () => {
      const result = transformer.parseJsxAttributes('class="test-class"');
      expect(result).toBe("{ class: 'test-class' }");
    });

    it('should parse style attributes correctly', () => {
      const result = transformer.parseJsxAttributes('style="opacity:0; color:red"');
      expect(result).toContain('opacity: \'0\'');
      expect(result).toContain('color: \'red\'');
    });

    it('should parse event handlers correctly', () => {
      const result = transformer.parseJsxAttributes('on-click={() => this.test()}');
      expect(result).toContain('onClick: () => this.test()');
    });

    it('should handle multiple attributes', () => {
      const result = transformer.parseJsxAttributes('class="test" style="opacity:0" on-click={() => test()}');
      expect(result).toContain("class: 'test'");
      expect(result).toContain('opacity: \'0\'');
      expect(result).toContain('onClick:');
    });
  });

  describe('parseJsxChildren', () => {
    it('should parse text content', () => {
      const result = transformer.parseJsxChildren('Hello World');
      expect(result).toEqual(["'Hello World'"]);
    });

    it('should parse expressions', () => {
      const result = transformer.parseJsxChildren('{data.title}');
      expect(result).toEqual(['data.title']);
    });

    it('should parse mixed content', () => {
      const result = transformer.parseJsxChildren('{data.title}{data.id}');
      expect(result).toEqual(['data.title', 'data.id']);
    });
  });
});
