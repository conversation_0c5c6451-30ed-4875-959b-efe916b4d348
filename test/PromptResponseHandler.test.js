const PromptResponseHandler = require('../src/ai/PromptResponseHandler');

describe('PromptResponseHandler', () => {
  let handler;

  beforeEach(() => {
    handler = new PromptResponseHandler({ verbose: false });
  });

  describe('formatTemplate', () => {
    test('应该正确格式化fix_result模板', () => {
      const result = handler.formatTemplate('fix_result', '修复后的代码', '修复说明');
      
      expect(result).toContain('<fix_result>');
      expect(result).toContain('<fixed_content>');
      expect(result).toContain('修复后的代码');
      expect(result).toContain('<changes_made>');
      expect(result).toContain('修复说明');
      expect(result).toContain('</fix_result>');
    });

    test('应该正确格式化analysis模板', () => {
      const fileList = '<file>src/App.vue</file>\n<file>src/main.js</file>';
      const result = handler.formatTemplate('analysis', fileList, '需要修复这些文件');

      expect(result).toContain('<analysis>');
      expect(result).toContain('<files_to_fix>');
      expect(result).toContain('<reasoning>');
    });

    test('应该抛出错误当格式不支持时', () => {
      expect(() => {
        handler.formatTemplate('unsupported_format', 'content');
      }).toThrow('不支持的模板类型: unsupported_format');
    });
  });

  describe('parseResponse', () => {
    test('应该解析fix_result格式', () => {
      const response = `
\`\`\`xml
<fix_result>
<fixed_content>
const app = createApp(App);
app.mount('#app');
</fixed_content>
<changes_made>
将Vue 2语法改为Vue 3语法
</changes_made>
</fix_result>
\`\`\`
      `;

      const result = handler.parseResponse(response);
      
      expect(result.success).toBe(true);
      expect(result.format).toBe('fix_result');
      expect(result.content).toContain('createApp(App)');
      expect(result.metadata).toContain('Vue 3语法');
    });

    test('应该解析analysis格式', () => {
      const response = `
\`\`\`xml
<analysis>
<files_to_fix>
<file>src/components/Header.vue</file>
<file>src/utils/api.js</file>
</files_to_fix>
<reasoning>
这些文件包含Vue 2语法需要升级
</reasoning>
</analysis>
\`\`\`
      `;

      const result = handler.parseResponse(response);
      
      expect(result.success).toBe(true);
      expect(result.format).toBe('analysis');
      expect(result.content).toContain('<file>src/components/Header.vue</file>');
      expect(result.metadata).toContain('Vue 2语法');
    });

    test('应该处理HTML实体编码', () => {
      const response = `
\`\`\`xml
<fix_result>
<fixed_content>
const template = '&lt;div&gt;Hello&lt;/div&gt;';
</fixed_content>
<changes_made>
修复模板字符串
</changes_made>
</fix_result>
\`\`\`
      `;

      const result = handler.parseResponse(response);
      
      expect(result.success).toBe(true);
      expect(result.content).toContain('<div>Hello</div>');
    });
    
    test('应该处理fixed_content中包含markdown代码块的情况', () => {
      const response = `
\`\`\`xml
<fix_result>
<fixed_content>
\`\`\`vue
<template>
  <div class="components-container">
    <el-button @click="() => (imagecropperShow = true)">
      Change Avatar
    </el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const imagecropperShow = ref(false);
</script>
\`\`\`
</fixed_content>
<changes_made>
将Vue 2组件转换为Vue 3 Composition API
</changes_made>
</fix_result>
\`\`\`
      `;

      const result = handler.parseResponse(response);
      
      expect(result.success).toBe(true);
      expect(result.format).toBe('fix_result');
      // 确认内容中不包含markdown代码块标记
      expect(result.content).not.toContain('```vue');
      expect(result.content).not.toContain('```');
      // 确认内容中包含实际的Vue代码
      expect(result.content).toContain('<template>');
      expect(result.content).toContain('<script setup>');
      expect(result.content).toContain('const imagecropperShow = ref(false);');
      expect(result.metadata).toContain('Vue 3 Composition API');
    });

    test('应该回退到代码块解析', () => {
      const response = `
\`\`\`javascript
const app = createApp(App);
app.mount('#app');
\`\`\`
      `;

      const result = handler.parseResponse(response);
      
      expect(result.success).toBe(true);
      expect(result.format).toBe('code_block');
      expect(result.content).toContain('createApp(App)');
    });
  });

  describe('parseFileList', () => {
    test('应该解析简单文件列表', () => {
      const content = `
<file>src/App.vue</file>
<file>src/main.js</file>
<file>src/components/Header.vue</file>
      `;

      const files = handler.parseFileList(content);
      
      expect(files).toHaveLength(3);
      expect(files).toContain('src/App.vue');
      expect(files).toContain('src/main.js');
      expect(files).toContain('src/components/Header.vue');
    });

    test('应该解析带类型的文件列表', () => {
      const content = `
<item type="file">src/App.vue</item>
<item type="directory">src/components</item>
<item type="file">package.json</item>
      `;

      const files = handler.parseFileList(content);
      
      expect(files).toHaveLength(3);
      expect(files[0]).toEqual({ type: 'file', path: 'src/App.vue' });
      expect(files[1]).toEqual({ type: 'directory', path: 'src/components' });
      expect(files[2]).toEqual({ type: 'file', path: 'package.json' });
    });
  });

  describe('parseToolCalls', () => {
    test('应该解析JSON格式的工具调用', () => {
      const response = `
\`\`\`json
{
  "tool_calls": [
    {
      "name": "read_file",
      "parameters": {
        "file_path": "src/App.vue"
      }
    },
    {
      "name": "list_files",
      "parameters": {
        "directory": "src/components",
        "pattern": "*.vue"
      }
    }
  ],
  "reasoning": "需要读取这些文件来理解项目结构"
}
\`\`\`
      `;

      const result = handler.parseToolCalls(response);
      
      expect(result.success).toBe(true);
      expect(result.toolCalls).toHaveLength(2);
      expect(result.toolCalls[0].name).toBe('read_file');
      expect(result.toolCalls[0].parameters.file_path).toBe('src/App.vue');
      expect(result.reasoning).toContain('项目结构');
    });

    test('应该回退到XML格式解析', () => {
      const response = `
<tool_calls>
<call>
<n>read_file</n>
<parameters>
<file_path>src/App.vue</file_path>
</parameters>
</call>
</tool_calls>
      `;

      const result = handler.parseToolCalls(response);
      
      expect(result.success).toBe(true);
      expect(result.toolCalls).toHaveLength(1);
      expect(result.toolCalls[0].name).toBe('read_file');
      expect(result.toolCalls[0].parameters.file_path).toBe('src/App.vue');
    });
  });

  describe('parseAnyResponse', () => {
    test('应该根据类型选择正确的解析方法', () => {
      const toolCallResponse = `
\`\`\`json
{
  "tool_calls": [
    {
      "name": "read_file",
      "parameters": {
        "file_path": "src/App.vue"
      }
    }
  ]
}
\`\`\`
      `;

      const result = handler.parseAnyResponse(toolCallResponse, 'tool_calls');
      
      expect(result.success).toBe(true);
      expect(result.toolCalls).toHaveLength(1);
    });
  });

  describe('validateXmlFormat', () => {
    test('应该验证正确的XML格式', () => {
      const validXml = `
<fix_result>
<fixed_content>
代码内容
</fixed_content>
<changes_made>
修改说明
</changes_made>
</fix_result>
      `;

      const isValid = handler.validateXmlFormat(validXml, 'fix_result');
      expect(isValid).toBe(true);
    });

    test('应该拒绝无效的XML格式', () => {
      const invalidXml = `
<fix_result>
代码内容
</fix_result>
      `;

      const isValid = handler.validateXmlFormat(invalidXml, 'fix_result');
      expect(isValid).toBe(false);
    });
  });
});
