const fs = require('fs');
const path = require('path');
const CustomVueTransformer = require('./src/frameworks/vue/CustomVueTransformer');

// 创建一个测试文件
const testVueFile = `<template>
  <div>
    <el-tree
      :render-content="renderContent"
      :data="treeData"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      treeData: []
    }
  },
  methods: {
    renderContent(h, { node, data }) {
      // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        <div class="connect_id_title">
          {data.title}{' '}
          <span class="connect_id_number" style="opacity:0">
            {data.id}
          </span>
        </div>
      )
    },
    expandAll(expand) {
      // 展开/折叠逻辑
    }
  }
}
</script>

<style scoped>
.connect_id_title {
  display: flex;
  align-items: center;
}
.connect_id_number {
  margin-left: 10px;
}
</style>`;

console.log('=== 原始Vue文件 ===');
console.log(testVueFile);

// 使用CustomVueTransformer进行转换
const transformer = new CustomVueTransformer();
const transformedContent = transformer.transform(testVueFile);

console.log('\n=== 转换后的Vue文件 ===');
console.log(transformedContent);

// 验证转换结果
console.log('\n=== 验证结果 ===');
const checks = [
  {
    name: '函数签名转换',
    test: transformedContent.includes('renderContent({ node, data'),
    expected: true
  },
  {
    name: 'h函数导入',
    test: transformedContent.includes("import { h } from 'vue'"),
    expected: true
  },
  {
    name: 'JSX转换为h函数',
    test: transformedContent.includes("h('div'") && transformedContent.includes("h('span'"),
    expected: true
  },
  {
    name: '移除原始JSX',
    test: !transformedContent.includes('<div class="connect_id_title">'),
    expected: true
  },
  {
    name: '保留其他代码',
    test: transformedContent.includes('<template>') && transformedContent.includes('<style'),
    expected: true
  }
];

checks.forEach(check => {
  const status = check.test === check.expected ? '✅' : '❌';
  console.log(`${status} ${check.name}: ${check.test}`);
});

// 保存转换后的文件用于检查
const outputPath = './test-transformed.vue';
fs.writeFileSync(outputPath, transformedContent);
console.log(`\n转换后的文件已保存到: ${outputPath}`);

console.log('\n=== 总结 ===');
const allPassed = checks.every(check => check.test === check.expected);
if (allPassed) {
  console.log('🎉 所有测试通过！renderContent JSX转换功能正常工作。');
} else {
  console.log('⚠️ 部分测试失败，需要进一步调试。');
}
