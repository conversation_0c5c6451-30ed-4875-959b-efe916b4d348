const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * Sass 错误诊断和修复系统
 * 智能分析 Sass 迁移中的常见错误并提供修复建议
 */
class SassErrorDiagnostic {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      verbose: false,
      autoFix: false,
      ...options
    };

    // 错误模式定义
    this.errorPatterns = [
      {
        id: 'undefined-variable',
        pattern: /Undefined variable: \$([a-zA-Z0-9_-]+)/,
        type: 'variable',
        severity: 'error',
        description: '未定义的变量',
        autoFixable: true
      },
      {
        id: 'module-loop',
        pattern: /Module loop: this module is already being loaded/,
        type: 'dependency',
        severity: 'error',
        description: '模块循环依赖',
        autoFixable: true
      },
      {
        id: 'file-not-found',
        pattern: /Could not find Sass file at: (.+)/,
        type: 'path',
        severity: 'error',
        description: '找不到 Sass 文件',
        autoFixable: true
      },
      {
        id: 'invalid-import',
        pattern: /@import rules are deprecated/,
        type: 'syntax',
        severity: 'warning',
        description: '@import 规则已废弃',
        autoFixable: true
      },
      {
        id: 'namespace-conflict',
        pattern: /There's already a module with namespace "([^"]+)"/,
        type: 'namespace',
        severity: 'error',
        description: '命名空间冲突',
        autoFixable: true
      }
    ];

    this.diagnosticResults = [];
    this.fixSuggestions = [];
  }

  /**
   * 执行错误诊断
   */
  async diagnose(errorOutput = '', filePath = null) {
    console.log(chalk.blue('🔍 开始 Sass 错误诊断...'));

    try {
      // 1. 分析错误输出
      if (errorOutput) {
        await this.analyzeErrorOutput(errorOutput, filePath);
      }

      // 2. 扫描项目文件
      await this.scanProjectFiles();

      // 3. 生成修复建议
      await this.generateFixSuggestions();

      // 4. 自动修复（如果启用）
      if (this.options.autoFix) {
        await this.applyAutoFixes();
      }

      // 5. 生成诊断报告
      const report = this.generateDiagnosticReport();

      console.log(chalk.green('✅ Sass 错误诊断完成'));

      return report;

    } catch (error) {
      console.error(chalk.red(`错误诊断失败: ${error.message}`));
      throw error;
    }
  }

  /**
   * 分析错误输出
   */
  async analyzeErrorOutput(errorOutput, filePath) {
    const lines = errorOutput.split('\n');

    for (const line of lines) {
      for (const pattern of this.errorPatterns) {
        const match = line.match(pattern.pattern);

        if (match) {
          const diagnostic = {
            id: pattern.id,
            type: pattern.type,
            severity: pattern.severity,
            description: pattern.description,
            message: line.trim(),
            file: filePath,
            match: match,
            autoFixable: pattern.autoFixable,
            timestamp: new Date()
          };

          this.diagnosticResults.push(diagnostic);

          if (this.options.verbose) {
            console.log(chalk.yellow(`发现错误: ${pattern.description} - ${line.trim()}`));
          }
        }
      }
    }
  }

  /**
   * 扫描项目文件
   */
  async scanProjectFiles() {
    const glob = require('glob');

    const sassFiles = glob.sync('**/*.{scss,sass}', {
      cwd: this.projectPath,
      absolute: true,
      ignore: ['node_modules/**', 'dist/**', 'build/**']
    });

    for (const filePath of sassFiles) {
      await this.analyzeFile(filePath);
    }
  }

  /**
   * 分析单个文件
   */
  async analyzeFile(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const relativePath = path.relative(this.projectPath, filePath);

      // 检查废弃的 @import 语句
      this.checkDeprecatedImports(content, relativePath);

      // 检查潜在的循环依赖
      this.checkPotentialLoops(content, relativePath);

      // 检查路径问题
      this.checkPathIssues(content, relativePath);

      // 检查变量使用
      this.checkVariableUsage(content, relativePath);

    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`分析文件失败 ${filePath}: ${error.message}`));
      }
    }
  }

  /**
   * 检查废弃的 @import 语句
   */
  checkDeprecatedImports(content, filePath) {
    const importRegex = /@import\s+['"][^'"]+['"];?/g;
    let match;

    while ((match = importRegex.exec(content)) !== null) {
      this.diagnosticResults.push({
        id: 'deprecated-import',
        type: 'syntax',
        severity: 'warning',
        description: '使用了废弃的 @import 语句',
        message: `在 ${filePath} 中发现 @import: ${match[0]}`,
        file: filePath,
        line: this.getLineNumber(content, match.index),
        match: match,
        autoFixable: true,
        timestamp: new Date()
      });
    }
  }

  /**
   * 检查潜在的循环依赖
   */
  checkPotentialLoops(content, filePath) {
    // 检查自引用
    const selfImportRegex = new RegExp(`@(?:import|use)\\s+['"][^'"]*${path.basename(filePath, path.extname(filePath))}[^'"]*['"]`, 'g');

    if (selfImportRegex.test(content)) {
      this.diagnosticResults.push({
        id: 'self-reference',
        type: 'dependency',
        severity: 'error',
        description: '文件自引用导致循环依赖',
        message: `${filePath} 引用了自身`,
        file: filePath,
        autoFixable: true,
        timestamp: new Date()
      });
    }
  }

  /**
   * 检查路径问题
   */
  checkPathIssues(content, filePath) {
    // 检查 ~ 别名使用
    const tildeRegex = /@(?:import|use)\s+['"]~([^'"]+)['"];?/g;
    let match;

    while ((match = tildeRegex.exec(content)) !== null) {
      this.diagnosticResults.push({
        id: 'tilde-alias',
        type: 'path',
        severity: 'warning',
        description: '使用了 ~ 别名，可能需要配置路径解析',
        message: `在 ${filePath} 中发现 ~ 别名: ${match[0]}`,
        file: filePath,
        line: this.getLineNumber(content, match.index),
        match: match,
        autoFixable: true,
        timestamp: new Date()
      });
    }
  }

  /**
   * 检查变量使用
   */
  checkVariableUsage(content, filePath) {
    // 检查未命名空间的变量使用（在 @use 上下文中）
    const hasUseStatements = /@use\s+/.test(content);

    if (hasUseStatements) {
      const variableRegex = /\$([a-zA-Z0-9_-]+)(?!\s*:)/g;
      let match;

      while ((match = variableRegex.exec(content)) !== null) {
        // 检查变量是否有命名空间前缀
        const beforeVar = content.substring(Math.max(0, match.index - 20), match.index);

        if (!/[a-zA-Z0-9_-]+\.$/.test(beforeVar)) {
          this.diagnosticResults.push({
            id: 'unnamespaced-variable',
            type: 'variable',
            severity: 'warning',
            description: '在 @use 上下文中使用了未命名空间的变量',
            message: `在 ${filePath} 中发现未命名空间的变量: $${match[1]}`,
            file: filePath,
            line: this.getLineNumber(content, match.index),
            match: match,
            autoFixable: false,
            timestamp: new Date()
          });
        }
      }
    }
  }

  /**
   * 获取行号
   */
  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  /**
   * 生成修复建议
   */
  async generateFixSuggestions() {
    for (const diagnostic of this.diagnosticResults) {
      const suggestion = await this.createFixSuggestion(diagnostic);
      if (suggestion) {
        this.fixSuggestions.push(suggestion);
      }
    }
  }

  /**
   * 创建修复建议
   */
  async createFixSuggestion(diagnostic) {
    switch (diagnostic.id) {
      case 'undefined-variable':
        return this.createVariableFixSuggestion(diagnostic);

      case 'module-loop':
        return this.createLoopFixSuggestion(diagnostic);

      case 'file-not-found':
        return this.createPathFixSuggestion(diagnostic);

      case 'deprecated-import':
        return this.createImportFixSuggestion(diagnostic);

      case 'tilde-alias':
        return this.createAliasFixSuggestion(diagnostic);

      default:
        return null;
    }
  }

  /**
   * 创建变量修复建议
   */
  createVariableFixSuggestion(diagnostic) {
    const variableName = diagnostic.match[1];

    return {
      id: `fix-${diagnostic.id}-${Date.now()}`,
      diagnostic: diagnostic,
      title: `修复未定义变量 $${variableName}`,
      description: `变量 $${variableName} 未定义，可能需要添加 @use 语句或检查变量名`,
      actions: [
        {
          type: 'add-use',
          description: `添加 @use 'src/styles' as *; 到文件顶部`,
          code: `@use 'src/styles' as *;`
        },
        {
          type: 'check-spelling',
          description: `检查变量名拼写是否正确`
        }
      ],
      autoFixable: false
    };
  }

  /**
   * 创建循环依赖修复建议
   */
  createLoopFixSuggestion(diagnostic) {
    return {
      id: `fix-${diagnostic.id}-${Date.now()}`,
      diagnostic: diagnostic,
      title: '修复循环依赖',
      description: '检测到模块循环依赖，需要重构依赖关系',
      actions: [
        {
          type: 'remove-self-reference',
          description: '移除文件中的自引用语句',
          autoFixable: true
        },
        {
          type: 'refactor-dependencies',
          description: '重构依赖关系，使用桶文件模式'
        }
      ],
      autoFixable: true
    };
  }

  /**
   * 创建路径修复建议
   */
  createPathFixSuggestion(diagnostic) {
    return {
      id: `fix-${diagnostic.id}-${Date.now()}`,
      diagnostic: diagnostic,
      title: '修复文件路径',
      description: '找不到指定的 Sass 文件',
      actions: [
        {
          type: 'check-path',
          description: '检查文件路径是否正确'
        },
        {
          type: 'configure-vite',
          description: '配置 Vite 的 loadPaths 选项'
        }
      ],
      autoFixable: false
    };
  }

  /**
   * 创建导入修复建议
   */
  createImportFixSuggestion(diagnostic) {
    const importStatement = diagnostic.match[0];
    const pathMatch = importStatement.match(/['"]([^'"]+)['"]/);
    const importPath = pathMatch ? pathMatch[1] : '';

    return {
      id: `fix-${diagnostic.id}-${Date.now()}`,
      diagnostic: diagnostic,
      title: '转换 @import 为 @use',
      description: '@import 规则已废弃，应该使用 @use',
      actions: [
        {
          type: 'replace-import',
          description: `将 ${importStatement} 替换为 @use 语句`,
          code: `@use '${importPath}' as *;`,
          autoFixable: true
        }
      ],
      autoFixable: true
    };
  }

  /**
   * 创建别名修复建议
   */
  createAliasFixSuggestion(diagnostic) {
    return {
      id: `fix-${diagnostic.id}-${Date.now()}`,
      diagnostic: diagnostic,
      title: '配置路径别名',
      description: '~ 别名需要正确的路径配置',
      actions: [
        {
          type: 'configure-vite',
          description: '在 vite.config.js 中配置 loadPaths',
          code: `css: {
  preprocessorOptions: {
    scss: {
      loadPaths: [path.resolve(__dirname, 'node_modules')]
    }
  }
}`
        }
      ],
      autoFixable: false
    };
  }

  /**
   * 应用自动修复
   */
  async applyAutoFixes() {
    console.log(chalk.blue('🔧 应用自动修复...'));

    let fixedCount = 0;

    for (const suggestion of this.fixSuggestions) {
      if (suggestion.autoFixable) {
        try {
          await this.applyFix(suggestion);
          fixedCount++;
        } catch (error) {
          console.warn(chalk.yellow(`自动修复失败: ${error.message}`));
        }
      }
    }

    if (fixedCount > 0) {
      console.log(chalk.green(`✅ 自动修复了 ${fixedCount} 个问题`));
    }
  }

  /**
   * 应用单个修复
   */
  async applyFix(suggestion) {
    const diagnostic = suggestion.diagnostic;

    if (diagnostic.id === 'module-loop' && diagnostic.file) {
      await this.removeSelfReference(diagnostic.file);
    } else if (diagnostic.id === 'deprecated-import' && diagnostic.file) {
      await this.convertImportToUse(diagnostic.file, diagnostic.match[0]);
    }
  }

  /**
   * 移除自引用语句
   */
  async removeSelfReference(filePath) {
    const fullPath = path.resolve(this.projectPath, filePath);
    const content = await fs.readFile(fullPath, 'utf8');
    const fileName = path.basename(filePath, path.extname(filePath));

    const selfRefRegex = new RegExp(`@(?:import|use)\\s+['"][^'"]*${fileName}[^'"]*['"](?:\\s*;)?`, 'g');
    const updatedContent = content.replace(selfRefRegex, '');

    await fs.writeFile(fullPath, updatedContent, 'utf8');

    if (this.options.verbose) {
      console.log(chalk.gray(`移除自引用: ${filePath}`));
    }
  }

  /**
   * 转换 @import 为 @use
   */
  async convertImportToUse(filePath, importStatement) {
    const fullPath = path.resolve(this.projectPath, filePath);
    const content = await fs.readFile(fullPath, 'utf8');

    const pathMatch = importStatement.match(/['"]([^'"]+)['"]/);
    if (pathMatch) {
      const importPath = pathMatch[1];
      const useStatement = `@use '${importPath}' as *;`;
      const updatedContent = content.replace(importStatement, useStatement);

      await fs.writeFile(fullPath, updatedContent, 'utf8');

      if (this.options.verbose) {
        console.log(chalk.gray(`转换导入语句: ${filePath}`));
      }
    }
  }

  /**
   * 生成诊断报告
   */
  generateDiagnosticReport() {
    const errorCount = this.diagnosticResults.filter(d => d.severity === 'error').length;
    const warningCount = this.diagnosticResults.filter(d => d.severity === 'warning').length;
    const fixableCount = this.fixSuggestions.filter(s => s.autoFixable).length;

    return {
      totalIssues: this.diagnosticResults.length,
      errors: errorCount,
      warnings: warningCount,
      fixableSuggestions: fixableCount,
      diagnostics: this.diagnosticResults,
      suggestions: this.fixSuggestions
    };
  }

  /**
   * 打印诊断结果
   */
  printDiagnosticResults(report) {
    console.log('\n' + chalk.bold('📊 Sass 错误诊断结果:'));
    console.log(`总问题数: ${report.totalIssues}`);
    console.log(`错误: ${chalk.red(report.errors)}`);
    console.log(`警告: ${chalk.yellow(report.warnings)}`);
    console.log(`可自动修复: ${chalk.green(report.fixableSuggestions)}`);

    if (report.diagnostics.length > 0) {
      console.log('\n' + chalk.bold('问题详情:'));

      report.diagnostics.forEach((diagnostic, index) => {
        const icon = diagnostic.severity === 'error' ? '❌' : '⚠️';
        console.log(`${icon} ${diagnostic.description}`);
        console.log(`   文件: ${diagnostic.file || '未知'}`);
        console.log(`   消息: ${diagnostic.message}`);

        if (index < report.diagnostics.length - 1) {
          console.log('');
        }
      });
    }

    if (report.suggestions.length > 0) {
      console.log('\n' + chalk.bold('修复建议:'));

      report.suggestions.forEach((suggestion, index) => {
        console.log(`${index + 1}. ${suggestion.title}`);
        console.log(`   ${suggestion.description}`);

        suggestion.actions.forEach(action => {
          const fixIcon = action.autoFixable ? '🔧' : '💡';
          console.log(`   ${fixIcon} ${action.description}`);
        });

        if (index < report.suggestions.length - 1) {
          console.log('');
        }
      });
    }
  }
}

module.exports = SassErrorDiagnostic;
