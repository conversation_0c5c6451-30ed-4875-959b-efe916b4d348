const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')

/**
 * Vue 3 兼容性检查器
 */
class PackageDependencyChecker {
	constructor (projectPath) {
		this.projectPath = projectPath
		this.packageJsonPath = path.join(projectPath, 'package.json')
		this.configPath = path.join(__dirname, '../../../config/package-recommend.json')
		this.config = null
		this.isVue3Project = false // 添加 Vue 3 项目标识
	}

	/**
	 * 加载配置文件
	 */
	async loadConfig() {
		try {
			this.config = await fs.readJson(this.configPath)
			// 检查项目是否已经是 Vue 3 项目
			await this.detectVueVersion()
		} catch (error) {
			console.error(chalk.red('❌ 无法加载配置文件:'), error.message)
			throw new Error(`Failed to load config file: ${this.configPath}`)
		}
	}

	/**
	 * 检测项目的 Vue 版本
	 */
	async detectVueVersion() {
		try {
			const packageJson = await fs.readJson(this.packageJsonPath)
			const allDeps = {
				...packageJson.dependencies,
				...packageJson.devDependencies
			}

			const vueVersion = allDeps.vue
			if (vueVersion) {
				// 检查是否为 Vue 3 版本
				this.isVue3Project = vueVersion.startsWith('3.') || vueVersion.startsWith('^3.') || vueVersion.startsWith('~3.')

				if (this.isVue3Project) {
					console.log(chalk.yellow('⚠️  检测到目标项目已经是 Vue 3 项目'))
				}
			}
		} catch (error) {
			console.warn(chalk.yellow('⚠️  无法检测 Vue 版本，将按 Vue 2 项目处理'))
		}
	}

	/**
	 * 检查所有依赖的 Vue 3 兼容性
	 */
	async checkCompatibility () {
		try {
			console.log(chalk.blue('🔍 开始检查依赖的 Vue 3 兼容性...'))

			// 加载配置文件
			await this.loadConfig()

			const packageJson = await fs.readJson(this.packageJsonPath)
			const allDeps = {
				...packageJson.dependencies,
				...packageJson.devDependencies
			}

			return await this.checkDependencies(allDeps)
		} catch (error) {
			console.error(chalk.red('❌ 依赖兼容性检查失败:'), error.message)
			throw error
		}
	}

	/**
	 * 检查依赖列表（使用本地映射表，快速检查）
	 */
	async checkDependencies (dependencies) {
		const results = {
			compatible: [],
			incompatible: [],
			unknown: [],
			total: Object.keys(dependencies).length
		}

		console.log(chalk.gray(`正在检查 ${results.total} 个依赖...`))

		for (const [depName, version] of Object.entries(dependencies)) {
			const compatibility = this.checkSingleDependencyLocal(depName, version)
			results[compatibility.status].push(compatibility)

			// 显示进度
			process.stdout.write('.')
		}

		console.log('\n')
		return results
	}

	/**
	 * 检查单个依赖的兼容性（本地快速检查）
	 */
	checkSingleDependencyLocal (depName, version) {
		// 跳过一些明显的系统依赖
		if (this.isSystemDependency(depName)) {
			return {
				name: depName,
				version,
				status: 'compatible',
				reason: 'System dependency'
			}
		}

		// 检查已知的不兼容包
		if (this.config.knownIncompatible[depName]) {
			const incompatibleInfo = this.config.knownIncompatible[depName]
			return {
				name: depName,
				version,
				status: 'incompatible',
				reason: incompatibleInfo.description || 'Known incompatible with Vue 3',
				alternatives: incompatibleInfo.alternatives
			}
		}

		// 检查已知的兼容包
		if (this.config.knownCompatible[depName]) {
			const compatibleInfo = this.config.knownCompatible[depName]
			return {
				name: depName,
				version,
				status: 'compatible',
				reason: compatibleInfo.description || 'Known compatible with Vue 3',
				recommendedVersion: compatibleInfo.version
			}
		}

		// 检查可能需要升级的包
		if (this.config.needsUpgrade[depName]) {
			const upgradeInfo = this.config.needsUpgrade[depName]
			return {
				name: depName,
				version,
				status: 'compatible',
				reason: upgradeInfo.description || 'Compatible with upgrade',
				recommendedVersion: upgradeInfo.version,
				note: upgradeInfo.note
			}
		}

		// 默认认为是兼容的（非 Vue 相关包）
		return {
			name: depName,
			version,
			status: 'compatible',
			reason: 'Non-Vue specific package, likely compatible'
		}
	}

	isSystemDependency (depName) {
		return this.config.systemDependencies.some(sysDep =>
			depName === sysDep || depName.startsWith(sysDep + '-') || depName.includes(sysDep)
		)
	}
}

module.exports = PackageDependencyChecker
