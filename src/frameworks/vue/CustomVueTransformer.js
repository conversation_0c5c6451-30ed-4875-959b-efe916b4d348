const SimpleRenderContentTransformer = require('./SimpleRenderContentTransformer');

/**
 * Vue 转换器
 * 提供自定义的 Vue 2 到 Vue 3 代码转换规则
 */
class CustomVueTransformer {
  constructor() {
    this.renderContentTransformer = new SimpleRenderContentTransformer();
  }
  /**
   * 应用自定义 Vue 转换规则
   * @param {string} code - 源代码
   * @returns {string} 转换后的代码
   */
  transform(code) {
    // 统一处理 gogocodeTransfer 导入 - 将所有相对路径替换为全局路径
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 处理其他可能的 gogocodeTransfer 导入格式
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 处理更复杂的相对路径
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/\.\.\/\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 替换 Element UI 导入
    code = code.replace(
      /import\s+{\s*([^}]+)\s*}\s+from\s+['"]element-ui['\"]/g,
      (match, imports) => {
        // 清理导入列表中的多余空格
        const cleanImports = imports.replace(/\s+/g, ' ').trim()
        return `import { ${cleanImports} } from 'element-plus'`
      }
    )

    // 替换 Element UI 完整导入
    code = code.replace(
      /import\s+ElementUI\s+from\s+['"]element-ui['\"]/g,
      'import ElementPlus from \'element-plus\''
    )

    // 替换 Element UI CSS 导入
    code = code.replace(
      /import\s+['"]element-ui\/lib\/theme-chalk\/index\.css['\"]/g,
      'import \'element-plus/dist/index.css\''
    )

    // 替换 Vue 导入 - 关键转换
    code = code.replace(
      /import\s+Vue\s+from\s+['"]vue['\"]/g,
      'import { createApp } from \'vue\''
    )

    // require('element-ui/package.json') to require('element-plus/package.json')
    code = code.replace(
      /require\(\s*['"]element-ui\/package\.json['\"]\s*\)/g,
      'require(\'element-plus/package.json\')'
    )

    // 替换 Vue 2 的全局 API
    // code = code.replace(/Vue\.extend\(/g, 'defineComponent(')
    // code = code.replace(/Vue\.component\(/g, 'app.component(')
    // code = code.replace(/Vue\.use\(/g, 'app.use(')
    // code = code.replace(/Vue\.config\./g, 'app.config.')
    // code = code.replace(/Vue\.mixin\(/g, 'app.mixin(')
    // code = code.replace(/Vue\.directive\(/g, 'app.directive(')
    // code = code.replace(/Vue\.filter\(/g, 'app.config.globalProperties.$filters = app.config.globalProperties.$filters || {}; app.config.globalProperties.$filters[')
    //
    // // 替换 Element UI 使用
    // code = code.replace(/Vue\.use\(ElementUI\)/g, 'app.use(ElementPlus)')
    //
    // // 替换 new Vue() 为 createApp() - 更复杂的处理
    // // 首先处理简单的 new Vue({...}).$mount('#app') 模式
    // code = code.replace(
    //   /new\s+Vue\(\s*{([^}]+)}\s*\)\.\$mount\(['"]#app['"]\)/g,
    //   'const app = createApp({\n$1\n})\napp.mount(\'#app\')'
    // )
    //
    // // 处理更复杂的 new Vue() 模式
    // code = code.replace(
    //   /new\s+Vue\(\s*{([\s\S]*?)}\s*\)\.\$mount\(['"]#app['"]\)/g,
    //   'const app = createApp({\n$1\n})\napp.mount(\'#app\')'
    // )
    //
    // // 处理没有 $mount 的 new Vue() 模式
    // code = code.replace(
    //   /new\s+Vue\(\s*{([\s\S]*?)}\s*\)/g,
    //   'const app = createApp({\n$1\n})'
    // )
    //
    // // 特殊处理 main.js 中的 Vue 实例创建
    // // 匹配 new Vue({ router, store, render: h => h(App) }).$mount('#app')
    // code = code.replace(
    //   /new\s+Vue\(\s*{\s*router,\s*store,\s*render:\s*h\s*=>\s*h\(App\)\s*}\s*\)\.\$mount\(['"]#app['"]\)/g,
    //   'const app = createApp({\n  router,\n  store,\n  render: h => h(App)\n})\napp.mount(\'#app\')'
    // )
    //
    // // 处理 Vue.config.productionTip
    // code = code.replace(
    //   /Vue\.config\.productionTip\s*=\s*false/g,
    //   '// Vue 3 中移除了 productionTip 配置'
    // )
    //
    // // 处理 Vue.mixin
    // code = code.replace(
    //   /Vue\.mixin\(\s*{([\s\S]*?)}\s*\)/g,
    //   'app.mixin({\n$1\n})'
    // )
    //
    // // 处理 Vue.directive
    // code = code.replace(
    //   /Vue\.directive\(\s*['"]([^'"]+)['"],\s*{([\s\S]*?)}\s*\)/g,
    //   'app.directive(\'$1\', {\n$2\n})'
    // )
    //
    // // 处理 Vue.filter - 转换为全局属性
    // code = code.replace(
    //   /Vue\.filter\(\s*['"]([^'"]+)['"],\s*function\s*\(([^)]*)\)\s*{([\s\S]*?)}\s*\)/g,
    //   'app.config.globalProperties.$filters = app.config.globalProperties.$filters || {}\napp.config.globalProperties.$filters[\'$1\'] = function($2) {\n$3\n}'
    // )

    // 替换 $refs 访问方式（需要更复杂的 AST 处理）
    // 这里只做简单的字符串替换示例

    // 处理 Vue 文件中的 SCSS @import 转换为 @use
    // code = this.transformScssImports(code)

    // 处理 Vue 2 JSX render 函数转换为 Vue 3 h 函数
    code = this.renderContentTransformer.transform(code)

    return code
  }

  /**
   * 转换 Vue 2 JSX render 函数为 Vue 3 h 函数
   * @param {string} code - 源代码
   * @returns {string} 转换后的代码
   */
  transformJsxRenderFunctions(code) {
    // 检查是否需要添加 h 函数导入
    let needsHImport = false;

    // 匹配 renderContent(h, { node, data }) 格式的函数
    const renderFunctionRegex = /(\w+)\s*\(\s*h\s*,\s*{\s*([^}]+)\s*}\s*\)\s*{([\s\S]*?)(?=\n\s*[},]|\n\s*$)/g;

    code = code.replace(renderFunctionRegex, (match, functionName, params, functionBody) => {
      needsHImport = true;

      // 转换函数签名：从 renderContent(h, { node, data }) 到 renderContent({ node, data })
      const newSignature = `${functionName}({ ${params.trim()} })`;

      // 转换函数体中的JSX为h函数调用
      let transformedBody = this.transformJsxToHFunction(functionBody);

      return `${newSignature} {${transformedBody}`;
    });

    // 如果需要h函数导入且还没有导入，则添加导入
    if (needsHImport && !code.includes("import { h }") && !code.includes("from 'vue'")) {
      // 检查是否已有Vue相关导入
      if (code.includes("import") && code.includes("vue")) {
        // 如果已有Vue导入，尝试添加h到现有导入中
        code = code.replace(
          /import\s*{\s*([^}]+)\s*}\s*from\s*['"]vue['"]/,
          (match, imports) => {
            if (!imports.includes('h')) {
              return `import { ${imports.trim()}, h } from 'vue'`;
            }
            return match;
          }
        );
      } else {
        // 如果没有Vue导入，在script标签后添加
        code = code.replace(
          /(<script[^>]*>)/,
          '$1\nimport { h } from \'vue\'\n'
        );
      }
    }

    return code;
  }

  /**
   * 转换JSX语法为h函数调用
   * @param {string} jsxCode - JSX代码
   * @returns {string} h函数代码
   */
  transformJsxToHFunction(jsxCode) {
    // 更精确的JSX到h函数转换
    let transformedCode = jsxCode;

    // 首先处理自闭合标签，如 <el-button type="primary" />
    transformedCode = transformedCode.replace(
      /<([\w-]+)([^>]*?)\/>/g,
      (match, tagName, attributes) => {
        const props = this.parseJsxAttributes(attributes);
        return `h('${tagName}'${props ? `, ${props}` : ''})`;
      }
    );

    // 然后处理完整的JSX元素，从最内层开始
    // 使用递归方式处理嵌套的JSX
    let hasChanges = true;
    while (hasChanges) {
      hasChanges = false;

      // 匹配最内层的JSX元素（不包含其他JSX元素的元素）
      const innerJsxRegex = /<([\w-]+)([^>]*?)>((?:[^<>]|{[^}]*})*?)<\/\1>/g;

      transformedCode = transformedCode.replace(innerJsxRegex, (match, tagName, attributes, content) => {
        hasChanges = true;
        const props = this.parseJsxAttributes(attributes);
        const children = this.parseJsxChildren(content);

        if (children.length === 0) {
          return `h('${tagName}'${props ? `, ${props}` : ''})`;
        } else if (children.length === 1) {
          return `h('${tagName}'${props ? `, ${props}` : ', null'}, ${children[0]})`;
        } else {
          return `h('${tagName}'${props ? `, ${props}` : ', null'}, [${children.join(', ')}])`;
        }
      });
    }

    return transformedCode;
  }

  /**
   * 解析JSX属性为对象字符串
   * @param {string} attributesStr - 属性字符串
   * @returns {string} 对象字符串
   */
  parseJsxAttributes(attributesStr) {
    if (!attributesStr || !attributesStr.trim()) {
      return null;
    }

    const props = [];

    // 处理 class 属性
    const classMatch = attributesStr.match(/class=["']([^"']+)["']/);
    if (classMatch) {
      props.push(`class: '${classMatch[1]}'`);
    }

    // 处理 style 属性
    const styleMatch = attributesStr.match(/style=["']([^"']+)["']/);
    if (styleMatch) {
      // 简单处理内联样式
      const styleObj = styleMatch[1].split(';')
        .filter(s => s.trim())
        .map(s => {
          const [key, value] = s.split(':').map(p => p.trim());
          const camelKey = key.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
          return `${camelKey}: '${value}'`;
        })
        .join(', ');
      props.push(`style: { ${styleObj} }`);
    }

    // 处理事件属性，如 on-click
    const eventMatches = attributesStr.matchAll(/on-(\w+)=\{([^}]+)\}/g);
    for (const match of eventMatches) {
      const eventName = match[1];
      const handler = match[2];
      props.push(`on${eventName.charAt(0).toUpperCase() + eventName.slice(1)}: ${handler}`);
    }

    return props.length > 0 ? `{ ${props.join(', ')} }` : null;
  }

  /**
   * 解析JSX子元素
   * @param {string} content - 内容字符串
   * @returns {Array} 子元素数组
   */
  parseJsxChildren(content) {
    const children = [];
    const trimmedContent = content.trim();

    if (!trimmedContent) {
      return children;
    }

    // 处理文本和表达式的混合内容
    const parts = trimmedContent.split(/(\{[^}]+\})/);

    for (const part of parts) {
      const trimmedPart = part.trim();
      if (!trimmedPart) continue;

      if (trimmedPart.startsWith('{') && trimmedPart.endsWith('}')) {
        // 这是一个表达式
        const expr = trimmedPart.slice(1, -1).trim();
        // 忽略空格表达式和空字符串表达式
        if (expr !== "' '" && expr !== '" "' && expr !== "''") {
          children.push(expr);
        }
      } else if (trimmedPart.includes('<')) {
        // 这可能是嵌套的JSX，递归处理
        children.push(this.transformJsxToHFunction(trimmedPart));
      } else if (trimmedPart) {
        // 这是纯文本，只有非空时才添加
        children.push(`'${trimmedPart}'`);
      }
    }

    return children;
  }

  /**
   * 转换 SCSS 中的 @import 为 @use
   * @param {string} code - Vue 文件代码
   * @returns {string} 转换后的代码
   */
  transformScssImports(code) {
    // 匹配 <style lang="scss" scoped> 或 <style lang="scss"> 块
    const styleRegex = /<style\s+lang=["']scss["'](?:\s+scoped)?\s*>([\s\S]*?)<\/style>/gi

    return code.replace(styleRegex, (match, styleContent) => {
      let transformedStyle = styleContent

      // 转换 @import 为 @use，但需要智能判断转换方式
      transformedStyle = transformedStyle.replace(
        /@import\s+['"]([^'"]+)['"];?/g,
        (importMatch, path) => {
          // 如果是外部 URL（http/https）或 .css 文件，保持 @import
          if (path.match(/^https?:/) || path.endsWith('.css')) {
            return importMatch // 保持原样
          }

          // 根据路径类型决定转换方式
          const filename = path.split('/').pop().replace(/^_/, '') // 移除前缀下划线

          // 对于常见的基础文件（变量、混合器等），使用 as * 以保持兼容性
          const baseFiles = ['variables', 'mixins', 'functions', 'utils', 'helpers', 'base', 'reset', 'normalize']
          const isBaseFile = baseFiles.some(base => filename.startsWith(base))

          if (isBaseFile) {
            return `@use '${path}' as *;`
          } else {
            // 对于组件或其他模块，使用命名空间
            const namespace = filename.replace(/\.(scss|sass)$/, '')
            return `@use '${path}' as ${namespace};`
          }
        }
      )

      // 返回更新后的 style 标签
      return match.replace(styleContent, transformedStyle)
    })
  }
}

module.exports = CustomVueTransformer
