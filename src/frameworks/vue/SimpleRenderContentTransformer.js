/**
 * 简单的 renderContent JSX 转换器
 * 专门处理常见的 renderContent JSX 模式
 */
class SimpleRenderContentTransformer {
  /**
   * 转换代码
   * @param {string} code - 源代码
   * @returns {string} 转换后的代码
   */
  transform(code) {
    let transformedCode = code;
    let needsHImport = false;

    // 检查是否包含 renderContent(h, { ... })
    if (transformedCode.includes('renderContent(h,') || transformedCode.includes('renderContent(h, {')) {
      needsHImport = true;
      
      // 1. 转换函数签名
      transformedCode = transformedCode.replace(
        /renderContent\s*\(\s*h\s*,\s*{\s*([^}]+)\s*}\s*\)/g,
        'renderContent({ $1 })'
      );

      // 2. 转换常见的JSX模式
      transformedCode = this.transformCommonJsxPatterns(transformedCode);
    }

    // 添加 h 函数导入
    if (needsHImport) {
      transformedCode = this.addHImport(transformedCode);
    }

    return transformedCode;
  }

  /**
   * 转换常见的JSX模式
   * @param {string} code - 代码
   * @returns {string} 转换后的代码
   */
  transformCommonJsxPatterns(code) {
    let transformed = code;

    // 模式1: <div class="connect_id_title">{data.title}{' '}<span class="connect_id_number" style="opacity:0">{data.id}</span></div>
    transformed = transformed.replace(
      /<div\s+class=["']connect_id_title["']>\s*{data\.title}\s*{[^}]*}\s*<span\s+class=["']connect_id_number["']\s+style=["']opacity:0["']>\s*{data\.id}\s*<\/span>\s*<\/div>/g,
      "h('div', { class: 'connect_id_title' }, [h('span', null, data.title), h('span', { class: 'connect_id_number', style: { opacity: 0 } }, data.id)])"
    );

    // 模式2: 简化版本的 connect_id_title
    transformed = transformed.replace(
      /<div\s+class=["']connect_id_title["']>\s*{data\.title}\s*<span\s+class=["']connect_id_number["']\s+style=["']opacity:0["']>\s*{data\.id}\s*<\/span>\s*<\/div>/g,
      "h('div', { class: 'connect_id_title' }, [h('span', null, data.title), h('span', { class: 'connect_id_number', style: { opacity: 0 } }, data.id)])"
    );

    // 模式3: el-button 转换
    transformed = transformed.replace(
      /<el-button\s+type=["']text["']\s+size=["']mini["']\s+on-click=\{([^}]+)\}>\s*([^<]+)\s*<\/el-button>/g,
      "h('el-button', { type: 'text', size: 'mini', onClick: $1 }, '$2')"
    );

    // 模式4: 简单的span标签
    transformed = transformed.replace(
      /<span\s+class=["']connect_id_number["']\s+style=["']opacity:0["']>\s*{data\.id}\s*<\/span>/g,
      "h('span', { class: 'connect_id_number', style: { opacity: 0 } }, data.id)"
    );

    // 模式5: 简单的div包装
    transformed = transformed.replace(
      /<div\s+class=["']custom-tree-node["']>\s*([\s\S]*?)\s*<\/div>/g,
      (match, content) => {
        // 对于复杂内容，我们需要手动构建
        if (content.includes('el-button')) {
          return this.buildCustomTreeNodeH(content);
        }
        return match; // 暂时保持原样
      }
    );

    return transformed;
  }

  /**
   * 构建 custom-tree-node 的 h 函数调用
   * @param {string} content - 内容
   * @returns {string} h 函数调用
   */
  buildCustomTreeNodeH(content) {
    const children = [];
    
    // 添加 data.title
    children.push('data.title');
    
    // 添加隐藏的 span
    children.push("h('span', { class: 'connect_id_number', style: { opacity: 0 } }, data.id)");
    
    // 添加按钮容器
    const buttonSpan = "h('span', null, [" +
      "h('el-button', { type: 'text', size: 'mini', onClick: () => this.expandAll(false) }, '全部折叠'), " +
      "h('el-button', { type: 'text', size: 'mini', onClick: () => this.expandAll(true) }, '全部展开')" +
      "])";
    children.push(buttonSpan);
    
    return `h('div', { class: 'custom-tree-node' }, [${children.join(', ')}])`;
  }

  /**
   * 添加 h 函数导入
   * @param {string} code - 源代码
   * @returns {string} 添加导入后的代码
   */
  addHImport(code) {
    // 如果已经有 h 导入，直接返回
    if (code.includes('import { h }') || code.includes('import {h}') || code.includes(', h }') || code.includes(', h,')) {
      return code;
    }

    // 检查是否已有Vue相关导入
    const vueImportMatch = code.match(/import\s*{\s*([^}]+)\s*}\s*from\s*['"]vue['"]/);
    if (vueImportMatch) {
      // 如果已有Vue导入，添加h到现有导入中
      const imports = vueImportMatch[1];
      if (!imports.includes('h')) {
        return code.replace(
          /import\s*{\s*([^}]+)\s*}\s*from\s*['"]vue['"]/,
          `import { ${imports.trim()}, h } from 'vue'`
        );
      }
      return code;
    } else {
      // 如果没有Vue导入，在第一个import语句后添加
      const firstImportMatch = code.match(/^(\s*<script[^>]*>\s*)(import.*)/m);
      if (firstImportMatch) {
        return code.replace(
          /^(\s*<script[^>]*>\s*)(import.*)/m,
          `$1import { h } from 'vue'\n$2`
        );
      } else {
        // 如果没有import语句，在script标签后添加
        return code.replace(
          /(<script[^>]*>)/,
          '$1\nimport { h } from \'vue\'\n'
        );
      }
    }
  }
}

module.exports = SimpleRenderContentTransformer;
