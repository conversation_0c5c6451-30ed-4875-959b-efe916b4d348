/**
 * Vue 2 renderContent JSX 转换器
 * 专门处理 renderContent(h, { node, data }) 函数的JSX到h函数转换
 */
class RenderContentTransformer {
  /**
   * 转换 Vue 2 JSX renderContent 为 Vue 3 h 函数
   * @param {string} code - 源代码
   * @returns {string} 转换后的代码
   */
  transform(code) {
    let transformedCode = code;
    let needsHImport = false;

    // 匹配 renderContent 函数的完整定义，包括多个return语句
    const renderContentRegex = /(renderContent\s*\(\s*h\s*,\s*{\s*([^}]+)\s*}\s*\)\s*{[\s\S]*?})\s*,/g;

    transformedCode = transformedCode.replace(renderContentRegex, (match, fullFunction, params) => {
      needsHImport = true;
      const transformed = this.transformRenderContentFunction(fullFunction, params.trim());
      return transformed + ',';
    });

    // 添加 h 函数导入
    if (needsHImport) {
      transformedCode = this.addHImport(transformedCode);
    }

    return transformedCode;
  }

  /**
   * 转换单个 renderContent 函数
   * @param {string} functionCode - 函数代码
   * @param {string} params - 参数列表
   * @returns {string} 转换后的函数代码
   */
  transformRenderContentFunction(functionCode, params) {
    let transformed = functionCode;

    // 1. 转换函数签名
    transformed = transformed.replace(
      /renderContent\s*\(\s*h\s*,\s*{\s*[^}]+\s*}\s*\)/,
      `renderContent({ ${params} })`
    );

    // 2. 转换JSX为h函数调用
    // 使用更通用的方法来转换所有JSX
    transformed = this.transformAllJsxElements(transformed);

    return transformed;
  }

  /**
   * 转换所有JSX元素为h函数调用
   * @param {string} code - 包含JSX的代码
   * @returns {string} 转换后的代码
   */
  transformAllJsxElements(code) {
    let transformed = code;

    // 处理自闭合标签
    transformed = transformed.replace(
      /<([\w-]+)([^>]*?)\/>/g,
      (match, tagName, attributes) => {
        const props = this.parseAttributes(attributes);
        return `h('${tagName}'${props !== 'null' ? `, ${props}` : ''})`;
      }
    );

    // 处理完整的JSX元素，从内到外递归处理
    let hasChanges = true;
    let iterations = 0;
    const maxIterations = 10; // 防止无限循环

    while (hasChanges && iterations < maxIterations) {
      hasChanges = false;
      iterations++;

      // 匹配最内层的JSX元素（不包含其他JSX标签的元素）
      const innerJsxRegex = /<([\w-]+)([^>]*?)>((?:[^<>]|{[^}]*})*?)<\/\1>/g;

      transformed = transformed.replace(innerJsxRegex, (match, tagName, attributes, content) => {
        hasChanges = true;
        const props = this.parseAttributes(attributes);
        const children = this.parseJsxContent(content);

        if (children.length === 0) {
          return `h('${tagName}'${props !== 'null' ? `, ${props}` : ''})`;
        } else if (children.length === 1) {
          return `h('${tagName}'${props !== 'null' ? `, ${props}` : ', null'}, ${children[0]})`;
        } else {
          return `h('${tagName}'${props !== 'null' ? `, ${props}` : ', null'}, [${children.join(', ')}])`;
        }
      });
    }

    return transformed;
  }

  /**
   * 解析JSX内容为子元素数组
   * @param {string} content - JSX内容
   * @returns {Array} 子元素数组
   */
  parseJsxContent(content) {
    const children = [];
    const trimmedContent = content.trim();

    if (!trimmedContent) {
      return children;
    }

    // 分割内容，处理表达式和文本
    const parts = trimmedContent.split(/(\{[^}]*\})/);

    for (const part of parts) {
      const trimmedPart = part.trim();
      if (!trimmedPart) continue;

      if (trimmedPart.startsWith('{') && trimmedPart.endsWith('}')) {
        // 这是一个表达式
        const expr = trimmedPart.slice(1, -1).trim();
        // 忽略空格表达式
        if (expr && expr !== "' '" && expr !== '" "') {
          children.push(expr);
        }
      } else if (trimmedPart.startsWith('h(')) {
        // 这已经是h函数调用了
        children.push(trimmedPart);
      } else if (trimmedPart) {
        // 这是纯文本
        children.push(`'${trimmedPart}'`);
      }
    }

    return children;
  }

  /**
   * 解析复杂内容（包含多个元素）
   * @param {string} content - 内容字符串
   * @returns {Array} 子元素数组
   */
  parseComplexContent(content) {
    const children = [];
    
    // 提取 {data.title} 等表达式
    const expressions = content.match(/{[^}]+}/g) || [];
    expressions.forEach(expr => {
      const cleanExpr = expr.slice(1, -1).trim();
      if (cleanExpr && !cleanExpr.includes("'") && !cleanExpr.includes('"')) {
        children.push(cleanExpr);
      }
    });

    // 提取 el-button 元素
    const buttonMatches = content.matchAll(/<el-button\s+([^>]*?)>\s*([\s\S]*?)\s*<\/el-button>/g);
    for (const match of buttonMatches) {
      const attributes = match[1];
      const buttonText = match[2].trim();
      const props = this.parseAttributes(attributes);
      children.push(`h('el-button', ${props}, '${buttonText}')`);
    }

    return children;
  }

  /**
   * 解析简单内容
   * @param {string} content - 内容字符串
   * @returns {Array} 子元素数组
   */
  parseSimpleContent(content) {
    const children = [];
    
    // 处理表达式 {data.xxx}
    const expressions = content.match(/{data\.\w+}/g) || [];
    expressions.forEach(expr => {
      const varName = expr.slice(1, -1); // 移除 { }
      children.push(varName);
    });

    // 处理 span 标签
    const spanMatches = content.matchAll(/<span\s+class=["']([^"']+)["']\s+style=["']([^"']+)["']>\s*{([^}]+)}\s*<\/span>/g);
    for (const match of spanMatches) {
      const className = match[1];
      const style = match[2];
      const expr = match[3];
      
      const styleObj = this.parseStyle(style);
      children.push(`h('span', { class: '${className}', style: ${JSON.stringify(styleObj)} }, ${expr})`);
    }

    return children;
  }

  /**
   * 解析元素属性
   * @param {string} attributesStr - 属性字符串
   * @returns {string} 属性对象字符串
   */
  parseAttributes(attributesStr) {
    const props = [];
    
    // 解析 type 属性
    const typeMatch = attributesStr.match(/type=["']([^"']+)["']/);
    if (typeMatch) {
      props.push(`type: '${typeMatch[1]}'`);
    }

    // 解析 size 属性
    const sizeMatch = attributesStr.match(/size=["']([^"']+)["']/);
    if (sizeMatch) {
      props.push(`size: '${sizeMatch[1]}'`);
    }

    // 解析事件处理器 on-click
    const clickMatch = attributesStr.match(/on-click=\{([^}]+)\}/);
    if (clickMatch) {
      props.push(`onClick: ${clickMatch[1]}`);
    }

    return props.length > 0 ? `{ ${props.join(', ')} }` : 'null';
  }

  /**
   * 解析样式字符串
   * @param {string} styleStr - 样式字符串
   * @returns {Object} 样式对象
   */
  parseStyle(styleStr) {
    const styleObj = {};
    const styles = styleStr.split(';').filter(s => s.trim());
    
    styles.forEach(style => {
      const [key, value] = style.split(':').map(s => s.trim());
      if (key && value) {
        const camelKey = key.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
        styleObj[camelKey] = isNaN(value) ? value : Number(value);
      }
    });
    
    return styleObj;
  }

  /**
   * 添加 h 函数导入
   * @param {string} code - 源代码
   * @returns {string} 添加导入后的代码
   */
  addHImport(code) {
    // 如果已经有 h 导入，直接返回
    if (code.includes('import { h }') || code.includes('import {h}')) {
      return code;
    }

    // 检查是否已有Vue相关导入
    if (code.includes('import') && code.includes('vue')) {
      // 如果已有Vue导入，尝试添加h到现有导入中
      return code.replace(
        /import\s*{\s*([^}]+)\s*}\s*from\s*['"]vue['"]/,
        (match, imports) => {
          if (!imports.includes('h')) {
            return `import { ${imports.trim()}, h } from 'vue'`;
          }
          return match;
        }
      );
    } else {
      // 如果没有Vue导入，在script标签后添加
      return code.replace(
        /(<script[^>]*>)/,
        '$1\nimport { h } from \'vue\'\n'
      );
    }
  }
}

module.exports = RenderContentTransformer;
